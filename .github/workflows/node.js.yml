# This workflow will do a clean installation of node dependencies, cache/restore them, build the source code and run tests across different versions of node
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-nodejs

name: Build & Style Check

permissions:
  contents: read

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: [22.x]
        # See supported Node.js release schedule at https://nodejs.org/en/about/releases/

    steps:
    - uses: actions/checkout@v3
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
# setup yarn
    - name: Setup Yarn
      run: npm install -g yarn
# install dependencies
    - name: Install Dependencies
      run: yarn install
# build
    - name: Build
      run: yarn build
# formatting check
    - name: Formatting Check
      run: yarn format:check
    
    - name: Lint Check
      run: yarn lint