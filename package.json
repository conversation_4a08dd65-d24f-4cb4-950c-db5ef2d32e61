{"name": "gemini-cli-worker", "version": "1.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "dev": "wrangler dev", "format": "prettier --write src", "format:check": "prettier --check src", "build": "wrangler deploy --dry-run --outdir=dist", "lint": "eslint --ext .ts src", "lint:fix": "eslint --ext .ts src --fix", "docker:build": "docker-compose build", "docker:dev": "docker-compose up --build", "docker:start": "docker-compose up", "docker:stop": "docker-compose down", "docker:clean": "docker-compose down -v && docker system prune -f", "docker:logs": "docker-compose logs -f gemini-worker", "docker:shell": "docker-compose exec gemini-worker sh"}, "dependencies": {"hono": "^4.8.4"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250705.0", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.1", "@types/node": "^24.0.10", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "eslint": "^9.30.1", "prettier": "^3.6.2", "typescript": "^5.4.5", "wrangler": "^4.23.0"}}